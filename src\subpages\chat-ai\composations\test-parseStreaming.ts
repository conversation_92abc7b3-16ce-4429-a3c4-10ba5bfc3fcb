import { parseStreamingData } from "./useParseStreaming";
import { str } from "./data";

/**
 * 测试 parseStreamingData 方法
 */
export function testParseStreamingData() {
  console.log("开始测试 parseStreamingData 方法...");
  
  try {
    const result = parseStreamingData(str);
    
    console.log("测试结果:");
    console.log("- 解析成功");
    console.log(`- 解析到 ${result.length} 个数据对象`);
    
    result.forEach((item, index) => {
      console.log(`- 数据 ${index + 1}:`, {
        event_type: item.event_type,
        message_id: item.message?.message_id,
        message_event_type: item.message?.event_type,
        has_content: !!item.message?.content
      });
    });
    
    return result;
  } catch (error) {
    console.error("测试失败:", error);
    throw error;
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testParseStreamingData();
}
