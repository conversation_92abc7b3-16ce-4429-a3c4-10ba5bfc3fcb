export const str = `data: {"message": {"id": "18f3735b-cea2-4178-8c56-00745649d7ac", "session_id": "3251", "message_id": "5199", "intention_id": "4887", "event_type": "node_started", "event_time": "2025-09-11 11:49:25", "content": "", "plan_id": "3924", "action_id": "9f05b098-10bc-4daf-b07c-44f5147e1ae6"}, "event_type": "node_started"}

data: {"message": {"id": "18f3735b-cea2-4178-8c56-00745649d7ac", "session_id": "3251", "message_id": "5199", "intention_id": "4887", "event_type": "node_finished", "event_time": "2025-09-11 11:49:25", "content": {"action_id": "9f05b098-10bc-4daf-b07c-44f5147e1ae6", "action_content": [{"orderId": "4500013082", "cabinetNo": "MNBU4080965", "contract": "901/2022（3101）", "stockWeight": "19986.801", "shelfStartExpDate": "2024/09/28", "prodStartDate": "2022/09/29", "prodEndDate": "2022/10/14", "declared": "", "finalVoyageEta": "", "portName": "", "transferHarbourDate": "", "transitHarbourName": "", "inboundNo": "ZR00003624", "warehouseName": "深圳锋润锋冷库（同乐）", "stockPiece": 924, "stockGrossWeight": "20584.626", "countryName": "巴西", "factoryCode": "SIF2146", "goodsName": "冷冻五花肉条", "pieceNum": 924, "materialDesc": "冷冻去骨猪肉块", "shelfDays": -333, "orderStatus": "", "shelfEndExpDate": "2024/10/13"}, {"orderId": "4500013082", "cabinetNo": "MNBU4080965", "contract": "901/2022（3101）", "stockWeight": "19986.801", "shelfStartExpDate": "2024/09/28", "prodStartDate": "2022/09/29", "prodEndDate": "2022/10/14", "declared": "", "finalVoyageEta": "", "portName": "", "transferHarbourDate": "", "transitHarbourName": "", "inboundNo": "ZR00003624", "warehouseName": "深圳锋润锋冷库（同乐）", "stockPiece": 924, "stockGrossWeight": "20584.626", "countryName": "巴西", "factoryCode": "SIF2146", "goodsName": "冷冻五花肉条", "pieceNum": 924, "materialDesc": "冷冻去骨猪肉块", "shelfDays": -333, "orderStatus": "", "shelfEndExpDate": "2024/10/13"}, {"orderId": "4500013082", "cabinetNo": "MNBU4080965", "contract": "901/2022（3101）", "stockWeight": "19986.801", "shelfStartExpDate": "2024/09/28", "prodStartDate": "2022/09/29", "prodEndDate": "2022/10/14", "declared": "", "finalVoyageEta": "", "portName": "", "transferHarbourDate": "", "transitHarbourName": "", "inboundNo": "ZR00003624", "warehouseName": "深圳锋润锋冷库（同乐）", "stockPiece": 924, "stockGrossWeight": "20584.626", "countryName": "巴西", "factoryCode": "SIF2146", "goodsName": "冷冻五花肉条", "pieceNum": 924, "materialDesc": "冷冻去骨猪肉块", "shelfDays": -333, "orderStatus": "", "shelfEndExpDate": "2024/10/13"}, {"orderId": "4500013082", "cabinetNo": "MNBU4080965", "contract": "901/2022（3101）", "stockWeight": "19986.801", "shelfStartExpDate": "2024/09/28", "prodStartDate": "2022/09/29", "prodEndDate": "2022/10/14", "declared": "", "finalVoyageEta": "", "portName": "", "transferHarbourDate": "", "transitHarbourName": "", "inboundNo": "ZR00003624", "warehouseName": "深圳锋润锋冷库（同乐）", "stockPiece": 924, "stockGrossWeight": "20584.626", "countryName": "巴西", "factoryCode": "SIF2146", "goodsName": "冷冻五花肉条", "pieceNum": 924, "materialDesc": "冷冻去骨猪肉块", "shelfDays": -333, "orderStatus": "", "shelfEndExpDate": "2024/10/13"}, {"orderId": "4500013082", "cabinetNo": "MNBU4080965", "contract": "901/2022（3101）", "stockWeight": "19986.801", "shelfStartExpDate": "2024/09/28", "prodStartDate": "2022/09/29", "prodEndDate": "2022/10/14", "declared": "", "finalVoyageEta": "", "portName": "", "transferHarbourDate": "", "transitHarbourName": "", "inboundNo": "ZR00003624", "warehouseName": "深圳锋润锋冷库（同乐）", "stockPiece": 924, "stockGrossWeight": "20584.626", "countryName": "巴西", "factoryCode": "SIF2146", "goodsName": "冷冻五花肉条", "pieceNum": 924, "materialDesc": "冷冻去骨猪肉块", "shelfDays": -333, "orderStatus": "", "shelfEndExpDate": "2024/10/13"}], "total": 300, "ai_search_key": "", "user_input": "查询我的库存", "is_card": 1, "card_type": "stockGoodsCard", "status": "succeed"}, "plan_id": "3924", "action_id": "9f05b098-10bc-4daf-b07c-44f5147e1ae6", "duration": 3.0}, "event_type": "node_finished"}

data: {"message": {"id": "18f3735b-cea2-4178-8c56-00745649d7ac", "session_id": "3251", "message_id": "5199", "intention_id": "4887", "event_type": "workflow_finished", "event_time": "2025-09-11 11:49:25", "description": "", "content": ""}, "event_type": "workflow_finished"}
`;
