import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每个JSON对象以"data:"开头
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 使用更精确的方法：逐字符扫描，找到每个完整的JSON对象
  let i = 0;
  while (i < data.length) {
    // 查找下一个 "data:" 标记
    const dataIndex = data.indexOf("data:", i);
    if (dataIndex === -1) {
      break;
    }

    // 跳过 "data:" 和可能的空白字符
    let jsonStart = dataIndex + 5;
    while (jsonStart < data.length && /\s/.test(data[jsonStart])) {
      jsonStart++;
    }

    if (jsonStart >= data.length || data[jsonStart] !== "{") {
      i = dataIndex + 5;
      continue;
    }

    // 使用括号计数找到完整的JSON对象
    const jsonStr = extractCompleteJson(data, jsonStart);
    if (jsonStr) {
      try {
        const parsed = JSON.parse(jsonStr);
        if (typeof parsed === "object" && parsed !== null) {
          parsedData.push(parsed);
        }
      } catch (error) {
        console.warn(
          `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${
            (error as Error).message
          }`,
        );
      }
      i = jsonStart + jsonStr.length;
    } else {
      i = dataIndex + 5;
    }
  }

  console.log("解析到的数据条数:", parsedData.length);
  console.log("解析结果:", parsedData);

  return parsedData;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 使用括号计数来正确处理嵌套结构和字符串中的特殊字符
 */
function extractCompleteJson(data: string, startIndex: number): string | null {
  let braceCount = 0;
  let inString = false;
  let escapeNext = false;
  let i = startIndex;

  while (i < data.length) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      i++;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      i++;
      continue;
    }

    if (char === '"' && !escapeNext) {
      inString = !inString;
    } else if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        if (braceCount === 0) {
          // 找到完整的JSON对象
          return data.substring(startIndex, i + 1);
        }
      }
    }

    i++;
  }

  // 如果没有找到完整的JSON对象，返回null
  return null;
}
