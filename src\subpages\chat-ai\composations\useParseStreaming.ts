import type { ChunkData } from "@/api/servers/chat/type";

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 * 支持增量式数据接收，处理被切断的JSON对象。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每个JSON对象以"data:"开头
 * @param isComplete - 是否是完整的数据块，默认为true。如果为false，会缓存未完成的数据
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(
  data: string,
  isComplete = true,
): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 合并缓存的数据和新数据
  const fullData = incompleteBuffer + data;

  // 重置缓存
  incompleteBuffer = "";

  // 使用更精确的方法：逐字符扫描，找到每个完整的JSON对象
  let i = 0;
  while (i < fullData.length) {
    // 查找下一个 "data:" 标记
    const dataIndex = fullData.indexOf("data:", i);
    if (dataIndex === -1) {
      // 如果没有找到 "data:" 标记，且数据不完整，缓存剩余数据
      if (!isComplete && i < fullData.length) {
        incompleteBuffer = fullData.substring(i);
      }
      break;
    }

    // 跳过 "data:" 和可能的空白字符
    let jsonStart = dataIndex + 5;
    while (jsonStart < fullData.length && /\s/.test(fullData[jsonStart])) {
      jsonStart++;
    }

    if (jsonStart >= fullData.length || fullData[jsonStart] !== "{") {
      i = dataIndex + 5;
      continue;
    }

    // 使用括号计数找到完整的JSON对象
    const result = extractCompleteJson(fullData, jsonStart);
    if (result.jsonStr) {
      try {
        const parsed = JSON.parse(result.jsonStr);
        if (typeof parsed === "object" && parsed !== null) {
          parsedData.push(parsed);
        }
      } catch (error) {
        console.warn(
          `无法解析JSON字符串: ${result.jsonStr.substring(0, 100)}...。错误: ${
            (error as Error).message
          }`,
        );
      }
      i = jsonStart + result.jsonStr.length;
    } else if (result.isIncomplete && !isComplete) {
      // JSON对象不完整，且数据流未结束，缓存从当前位置开始的所有数据
      incompleteBuffer = fullData.substring(dataIndex);
      break;
    } else {
      // JSON对象格式错误或数据流已结束，跳过
      i = dataIndex + 5;
    }
  }

  console.log("解析到的数据条数:", parsedData);
  console.log("缓存的数据长度:", incompleteBuffer.length);

  return parsedData;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJson(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isIncomplete: false,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const lastState = dp[dp.length - 1];
  const isIncomplete =
    lastState && lastState.isValid && lastState.braceCount > 0;

  return { jsonStr: null, isIncomplete };
}

/**
 * 重置解析器的内部缓存
 * 在开始新的流式数据解析时调用
 */
export function resetStreamingParser(): void {
  incompleteBuffer = "";
}

/**
 * 获取当前缓存的数据长度（用于调试）
 */
export function getBufferLength(): number {
  return incompleteBuffer.length;
}
