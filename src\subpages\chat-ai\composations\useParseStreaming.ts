import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每行以"data:"开头
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 按行分割数据
  const lines = data.split("\n");

  for (const line of lines) {
    const trimmedLine = line.trim();

    // 跳过空行
    if (!trimmedLine) {
      continue;
    }

    // 检查是否以"data:"开头
    if (trimmedLine.startsWith("data:")) {
      // 提取"data:"后面的JSON字符串
      const jsonStr = trimmedLine.substring(5).trim(); // 去掉"data:"前缀

      if (jsonStr) {
        try {
          const parsed = JSON.parse(jsonStr);
          if (typeof parsed === "object" && parsed !== null) {
            parsedData.push(parsed);
          }
        } catch (error) {
          console.warn(
            `无法解析JSON字符串: ${jsonStr}。错误: ${(error as Error).message}`,
          );
          continue;
        }
      }
    }
  }

  // 如果没有找到任何"data:"前缀的行，尝试解析整个字符串
  if (parsedData.length === 0) {
    const cleanData = data.replace(/data:/g, "").trim();
    try {
      const parsed = JSON.parse(cleanData);
      if (typeof parsed === "object" && parsed !== null) {
        if (Array.isArray(parsed)) {
          parsedData.push(...parsed);
        } else {
          parsedData.push(parsed);
        }
      }
    } catch (error) {
      console.warn(
        `无法解析JSON字符串: ${cleanData}。错误: ${(error as Error).message}`,
      );
    }
  }

  console.log("解析到的数据条数:", parsedData.length);
  console.log("解析结果:", parsedData);

  return parsedData;
}
