import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每个JSON对象以"data:"开头
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 使用更精确的方法：逐字符扫描，找到每个完整的JSON对象
  let i = 0;
  while (i < data.length) {
    // 查找下一个 "data:" 标记
    const dataIndex = data.indexOf("data:", i);
    if (dataIndex === -1) {
      break;
    }

    // 跳过 "data:" 和可能的空白字符
    let jsonStart = dataIndex + 5;
    while (jsonStart < data.length && /\s/.test(data[jsonStart])) {
      jsonStart++;
    }

    if (jsonStart >= data.length || data[jsonStart] !== "{") {
      i = dataIndex + 5;
      continue;
    }

    // 使用括号计数找到完整的JSON对象
    const jsonStr = extractCompleteJson(data, jsonStart);
    if (jsonStr) {
      try {
        const parsed = JSON.parse(jsonStr);
        if (typeof parsed === "object" && parsed !== null) {
          parsedData.push(parsed);
        }
      } catch (error) {
        console.warn(
          `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${
            (error as Error).message
          }`,
        );
      }
      i = jsonStart + jsonStr.length;
    } else {
      i = dataIndex + 5;
    }
  }

  console.log("解析到的数据条数:", parsedData.length);
  console.log("解析结果:", parsedData);

  return parsedData;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 */
function extractCompleteJson(data: string, startIndex: number): string | null {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return null;
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return data.substring(startIndex, startIndex + i + 1);
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  // 没有找到完整的JSON对象
  return null;
}
